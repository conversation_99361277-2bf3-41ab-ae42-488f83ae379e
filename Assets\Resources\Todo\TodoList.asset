%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d9dfa825770c5486b9d95384bc7f7a1a, type: 3}
  m_Name: TodoList
  m_EditorClassIdentifier: 
  owners:
  - name: Kerry
    color: {r: 1, g: 0.4669811, b: 0.4669811, a: 1}
    index: 0
  - name: Ryan
    color: {r: 1, g: 0.9911243, b: 0.5896226, a: 1}
    index: 1
  - name: Other
    color: {r: 0.4198113, g: 1, b: 1, a: 1}
    index: 2
  items:
  - owner:
      name: Other
      color: {r: 0.4198113, g: 1, b: 1, a: 1}
      index: 2
    task: reorder level list
    isComplete: 0
  - owner:
      name: <PERSON>
      color: {r: 1, g: 0.9911243, b: 0.5896226, a: 1}
      index: 1
    task: bugfix diagonal force through walls
    isComplete: 0
  - owner:
      name: Ryan
      color: {r: 1, g: 0.9911243, b: 0.5896226, a: 1}
      index: 1
    task: cardinal force through walls
    isComplete: 0
  - owner:
      name: Other
      color: {r: 0.4198113, g: 1, b: 1, a: 1}
      index: 2
    task: main menu redesign
    isComplete: 0
  - owner:
      name: Kerry
      color: {r: 1, g: 0.4669811, b: 0.4669811, a: 1}
      index: 0
    task: restart doesn't do a reload scene and just resets the grid
    isComplete: 1
  - owner:
      name: Kerry
      color: {r: 1, g: 0.4669811, b: 0.4669811, a: 1}
      index: 0
    task: Camera needs to be rescaled when going from level to level select
    isComplete: 1
  - owner:
      name: Kerry
      color: {r: 1, g: 0.4669811, b: 0.4669811, a: 1}
      index: 0
    task: fix reloading while playing creating visual bugs.
    isComplete: 1
  - owner:
      name: Ryan
      color: {r: 1, g: 0.9911243, b: 0.5896226, a: 1}
      index: 1
    task: asethetic stuff
    isComplete: 0
  - owner:
      name: Kerry
      color: {r: 1, g: 0.4669811, b: 0.4669811, a: 1}
      index: 0
    task: blocks that cant be frozen
    isComplete: 1
  - owner:
      name: Ryan
      color: {r: 1, g: 0.9911243, b: 0.5896226, a: 1}
      index: 1
    task: recreate concept block in color schemes
    isComplete: 0
  - owner:
      name: Kerry
      color: {r: 1, g: 0.4669811, b: 0.4669811, a: 1}
      index: 0
    task: change color pallete to be colors and not material
    isComplete: 1
  - owner:
      name: Kerry
      color: {r: 1, g: 0.4669811, b: 0.4669811, a: 1}
      index: 0
    task: change frozen visual effect to be an overlay that still shows base block
    isComplete: 1
  - owner:
      name: Ryan
      color: {r: 1, g: 0.9911243, b: 0.5896226, a: 1}
      index: 1
    task: base level asethetic finalized
    isComplete: 0
  - owner:
      name: Ryan
      color: {r: 1, g: 0.9911243, b: 0.5896226, a: 1}
      index: 1
    task: base island with small margin
    isComplete: 0
  - owner:
      name: Ryan
      color: {r: 1, g: 0.9911243, b: 0.5896226, a: 1}
      index: 1
    task: try real world buttons and make them look good
    isComplete: 0
  - owner:
      name: Ryan
      color: {r: 1, g: 0.9911243, b: 0.5896226, a: 1}
      index: 1
    task: tinker and finalise depth fog and gradient approach
    isComplete: 0
  - owner:
      name: Kerry
      color: {r: 1, g: 0.4669811, b: 0.4669811, a: 1}
      index: 0
    task: level property if it starts autoplaying or not
    isComplete: 1
  - owner:
      name: Kerry
      color: {r: 1, g: 0.4669811, b: 0.4669811, a: 1}
      index: 0
    task: Add icon to show canBeFrozen state
    isComplete: 1
